import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { getPaymentService, getCurrentPaymentProvider } from "@/services/payment";
import { insertOrder, updateOrderPaymentSession } from "@/models/order";
import { orders } from "@/db/schema";
import { getSnowId } from "@/lib/hash";
import { getIsoTimestr } from "@/lib/time";
import { getUserByEmail } from "@/models/user";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { code: 401, message: "请先登录" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { plan_id, credits, price, currency, interval, valid_months } = body;

    // 验证必要参数
    if (!plan_id || !credits || !price || !currency) {
      return NextResponse.json(
        { code: 400, message: "缺少必要参数" },
        { status: 400 }
      );
    }

    // 获取用户信息
    const user = await getUserByEmail(session.user.email);
    if (!user) {
      return NextResponse.json(
        { code: 404, message: "用户不存在" },
        { status: 404 }
      );
    }

    // 生成订单号
    const order_no = getSnowId();
    const now = getIsoTimestr();
    const paymentProvider = getCurrentPaymentProvider();

    // 计算金额（转换为分）
    const amount = Math.round(price * 100);
    const is_subscription = interval !== 'one-time';

    // 创建订单记录
    const order = {
      order_no,
      created_at: now,
      user_uuid: user.id,
      user_email: session.user.email,
      amount,
      interval: interval || 'one-time',
      expired_at: valid_months > 0 ?
        new Date(Date.now() + valid_months * 30 * 24 * 60 * 60 * 1000).toISOString() :
        null,
      status: "pending",
      payment_provider: paymentProvider,
      credits,
      currency,
      product_id: plan_id,
      product_name: `订阅计划 - ${plan_id}`,
      valid_months: valid_months || 0,
    };

    await insertOrder(order as typeof orders.$inferInsert);

    // 使用统一的支付服务
    const paymentService = getPaymentService();

    const paymentParams = {
      order_no: order_no,
      product_id: plan_id,
      product_name: `订阅计划 - ${plan_id}`,
      amount: amount,
      currency: currency,
      interval: interval || 'one-time',
      credits: credits,
      user_email: session.user.email,
      user_uuid: user.id,
      valid_months: valid_months || 0,
      success_url: `${process.env.NEXT_PUBLIC_WEB_URL}/pay-success/{CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_WEB_URL}/pricing`,
      is_subscription: is_subscription,
    };

    const paymentSession = await paymentService.createPaymentSession(paymentParams);

    // 更新订单支付会话信息
    await updateOrderPaymentSession(
      order_no,
      paymentProvider,
      paymentSession.session_id,
      JSON.stringify(paymentParams)
    );

    return NextResponse.json({
      code: 0,
      message: "支付会话创建成功",
      data: {
        order_no: order_no,
        session_id: paymentSession.session_id,
        redirect_url: paymentSession.redirect_url,
        payment_url: paymentSession.redirect_url, // 兼容旧版本
        payment_provider: paymentProvider,
        public_key: paymentSession.public_key,
      }
    });

  } catch (error) {
    console.error("Subscribe error:", error);
    return NextResponse.json(
      { code: 500, message: "服务器错误" },
      { status: 500 }
    );
  }
}
