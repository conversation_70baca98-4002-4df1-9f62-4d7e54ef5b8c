"use client";
import React, { useState } from "react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useAppContext } from "@/contexts/app";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

interface CreditRecord {
  id: number;
  trans_no: string;
  created_at: string;
  trans_type: string;
  credits: number;
  order_no?: string;
  expired_at?: string;
}

// 交易类型显示映射
const getTransTypeDisplay = (transType: string): string => {
  const typeMap: { [key: string]: string } = {
    'new_user': '新用户奖励',
    'order_pay': '订阅充值',
    'system_add': '系统赠送',
    'ping': '测试消费',
    'remove_watermark': '去水印',
  };
  return typeMap[transType] || transType;
};

export default function UserCenterPage() {
  const [tab, setTab] = useState<'log' | 'info'>("log");
  const [creditRecords, setCreditRecords] = useState<CreditRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const { user } = useAppContext();
  const router = useRouter();

  // 获取积分消费记录
  const fetchCreditRecords = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const response = await fetch('/api/get-user-credit-records', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ page: 1, limit: 100 }),
      });

      if (response.ok) {
        const { code, data } = await response.json();
        if (code === 0) {
          setCreditRecords(data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch credit records:', error);
    } finally {
      setLoading(false);
    }
  };

  // 检查用户是否已登录，如果未登录则重定向到登录页面
  useEffect(() => {
    if (!user) {
      router.push('/auth/signin?callbackUrl=' + encodeURIComponent('/i/user-center'));
    } else {
      fetchCreditRecords();
    }
  }, [user, router]);

  // 如果用户未登录，显示加载状态
  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-purple-50 flex flex-col items-center justify-center">
        <div className="text-lg">正在加载...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-purple-50 flex flex-col items-center py-16">
      {/* 如需调整白色卡片宽度，可修改max-w-7xl为max-w-6xl、max-w-4xl等，或直接用w-[1200px]自定义 */}
      <div className="w-full max-w-7xl bg-white rounded-xl shadow-2xl p-12 flex flex-col md:flex-row gap-12">
        {/* 左侧：头像+按钮 */}
        <div className="flex flex-col items-center md:items-start min-w-[200px]">
          <div className="flex items-center gap-6 mb-8">
            <Avatar className="w-20 h-20">
              <AvatarImage src={user.avatar_url} alt={user.nickname} />
              <AvatarFallback>{user.nickname?.charAt(0) || 'U'}</AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="text-gray-500 text-base">剩余积分</span>
              <span className="text-3xl font-bold text-green-600">{user.credits?.left_credits || 0}</span>
            </div>
          </div>
          <Button
            className={`w-40 mb-4 ${tab === "log" ? "bg-blue-500 text-white" : "bg-gray-100 text-gray-700"}`}
            onClick={() => setTab("log")}
            variant="ghost"
            size="lg"
          >
            积分消费明细
          </Button>
          <Button
            className={`w-40 ${tab === "info" ? "bg-blue-500 text-white" : "bg-gray-100 text-gray-700"}`}
            onClick={() => setTab("info")}
            variant="ghost"
            size="lg"
          >
            账户信息
          </Button>
        </div>
        {/* 右侧内容区 */}
        <div className="flex-1 min-w-0">
          {tab === "log" ? (
            <div>
              <div className="text-2xl font-bold mb-6">积分消费明细</div>
              <div className="h-96 overflow-y-auto pr-2 border rounded-2xl bg-blue-50/40 shadow-inner">
                {/* 表头 */}
                <div className="grid grid-cols-4 font-semibold text-blue-700 bg-blue-100 py-3 px-2 sticky top-0 z-10 rounded-t-2xl border-b border-blue-200">
                  <div className="text-center">交易时间</div>
                  <div className="text-center">交易类型</div>
                  <div className="text-center">积分变化</div>
                  <div className="text-center">交易号</div>
                </div>
                {loading ? (
                  <div className="flex justify-center items-center py-8">
                    <div className="text-gray-500">加载中...</div>
                  </div>
                ) : (
                  <ul className="divide-y divide-blue-100">
                    {creditRecords.length > 0 ? (
                      creditRecords.map((record) => (
                        <li key={record.id} className="grid grid-cols-4 items-center py-3 px-2">
                          <div className="text-gray-600 text-center text-sm">
                            {new Date(record.created_at).toLocaleString('zh-CN')}
                          </div>
                          <div className="text-gray-700 text-center text-sm">
                            {getTransTypeDisplay(record.trans_type)}
                          </div>
                          <div className={`font-bold text-center text-sm ${
                            record.credits > 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {record.credits > 0 ? '+' : ''}{record.credits}
                          </div>
                          <div className="text-gray-500 text-center text-xs">
                            {record.trans_no.slice(-8)}
                          </div>
                        </li>
                      ))
                    ) : (
                      <li className="py-8 text-center text-gray-500">
                        暂无积分消费记录
                      </li>
                    )}
                  </ul>
                )}
              </div>
            </div>
          ) : (
            <div>
              <div className="text-2xl font-bold mb-6">账户信息</div>
              <div className="bg-gray-50 rounded-2xl p-8 flex flex-col gap-6 w-full max-w-lg shadow">
                <div className="flex gap-6 items-center">
                  <Avatar className="w-16 h-16">
                    <AvatarImage src={user.avatar_url} alt={user.nickname} />
                    <AvatarFallback>{user.nickname?.charAt(0) || 'U'}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-bold text-xl">{user.nickname}</div>
                    <div className="text-gray-500 text-base">昵称</div>
                  </div>
                </div>
                <div className="flex flex-col gap-2 mt-2">
                  <div className="text-gray-700 text-base"><span className="font-semibold">ID：</span>{user.uuid}</div>
                  <div className="text-gray-700 text-base"><span className="font-semibold">邮箱：</span>{user.email}</div>
                </div>

                {/* Payment Method Info */}
                <div className="border-t pt-4 mt-4">
                  <div className="text-lg font-semibold mb-3">支付方式</div>
                  <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg p-4 border border-blue-200">
                    <div className="flex items-center gap-3 mb-2">
                      <img src="/imgs/logos/paypal.png" alt="PayPal" className="h-6" />
                      <span className="font-medium">PayPal</span>
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">已启用</span>
                    </div>
                    <p className="text-sm text-gray-600">
                      安全、快速的国际支付方式，支持信用卡和PayPal余额支付
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 