import { respOk } from "@/lib/resp";
import { handlePaymentSession } from "@/services/order";
import { getPaymentService } from "@/services/payment";

/**
 * PayPal Webhook处理
 */
export async function POST(req: Request) {
  try {
    const body = await req.text();
    const signature = req.headers.get("paypal-transmission-signature") || "";
    
    const paymentService = getPaymentService();
    
    // 验证webhook签名
    const event = paymentService.verifyWebhookSignature(
      body,
      signature,
      process.env.PAYPAL_WEBHOOK_SECRET || ""
    );

    console.log("PayPal notify event: ", event);

    // 处理不同的事件类型
    switch (event.event_type) {
      case "PAYMENT.CAPTURE.COMPLETED": {
        // 一次性支付完成
        const resource = event.resource;
        if (resource && resource.custom_id) {
          // 通过custom_id获取订单信息
          const session = await paymentService.getPaymentSession(resource.id);
          await handlePaymentSession(session);
        }
        break;
      }
      
      case "BILLING.SUBSCRIPTION.ACTIVATED": {
        // 订阅激活
        const resource = event.resource;
        if (resource && resource.custom_id) {
          const session = await paymentService.getPaymentSession(resource.id);
          await handlePaymentSession(session);
        }
        break;
      }

      default:
        console.log("PayPal event not handled: ", event.event_type);
    }

    return respOk();
  } catch (e: any) {
    console.log("PayPal notify failed: ", e);
    return Response.json(
      { error: `Handle PayPal notify failed: ${e.message}` },
      { status: 500 }
    );
  }
}
